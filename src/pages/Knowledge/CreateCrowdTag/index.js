import React, { Fragment } from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Row, Col, Select, Input, Button, Checkbox, Radio, Card, message, Popover, Switch } from 'antd';
import { connect } from 'dva';
import { Link }  from 'umi';
import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import GetUser from '@/components/GetUser';
import MyCascader from '@/components/Cascader';
import { getParentKey, getTreeLevel } from '@/utils/tree';
import { OverTimeType } from '@/utils/utils';
import { formatSubmit } from './common/utils'
import CreateTagEnum from '../components/CreateTagEnum';
import MyInputNumber from './components/MyInputNumber';
import Editor from '@monaco-editor/react';
import CodeMirror from '@/components/CodeMirror/CodeMirrorInput';
import SqlParams from './components/SqlParams';
import EditableTreeTable from './components/EditableTreeTable';
import { LABEL_DATA_SOURCE } from './common/constants';
import { isArrayEmptyOrHasObject } from './common/utils';
import ServiceParams from './components/ServiceParams';
import styles from './index.less';
import get from 'lodash/get';


const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 5 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const FORM_ITEM_LAB = {
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 24 },
  },
}

const FormItem = Form.Item;
const { TextArea } = Input;
const RadioGroup = Radio.Group;
const CheckboxGroup = Checkbox.Group;
const { Option } = Select;
const NOOP = () => { };

 // 安全提示
export const SAFE_TIP = (
  <div>
    <p>L1：该标签对所有人公开权限，不需要申请</p>
    <p>L2：低风险等级，需要申请，最长3年</p>
    <p>L3：中风险等级，需要申请，最长3年</p>
    <p>L4：高风险等级，需要申请，最长1年</p>
  </div>
);

@connect(state => ({ createCrowdTag: state.createCrowdTag, user: state.user }))
@Form.create(
  {
    onValuesChange: (props, values) => {
      const {
        dispatch,
      } = props;
      if (Object.keys(values).includes('serviceParams')) {
        const data = get(values, 'serviceParams', '')
        dispatch({
          type: 'createCrowdTag/editorHsfList',
          payload: data
        })
      }
    }
  }
)
class CreateCrowdTag extends React.PureComponent {
  // 不过期的时间 @泽林
  longOverTime = 9223372036854775000;


  componentWillReceiveProps(nextProps) {
    if (
      nextProps !== this.props &&
      nextProps.createCrowdTag.isAddGrant !== this.props.createCrowdTag.isAddGrant &&
      nextProps.createCrowdTag.isAddGrant === false
    ) {
      this.props.form.resetFields(['tableName', 'partitionField', 'primaryKey', 'isGranted']);
    }
  }

  getCategoryInitialValue = value => {
    if (!value) {
      return undefined;
    }

    const ret = [value];
    let curKey = value;
    const {
      createCrowdTag: { treeData },
    } = this.props;

    const level = getTreeLevel(0, treeData);
    // eslint-disable-next-line no-plusplus
    for (let i = level - 1; i > 0; i--) {
      curKey = getParentKey(curKey, treeData);
      if (curKey) {
        ret.unshift(parseInt(curKey, 10));
      }
    }

    return ret;
  };

  onTableSearch = val => {
    const { dispatch } = this.props;
    // 查询odps表
    dispatch({
      type: 'createCrowdTag/queryOdpsTable',
      payload: {
        keyword: val,
      },
    });
  };

  toggle = () => {
    const {
      dispatch,
      createCrowdTag: { isAddGrant },
      form: { setFieldsValue },
    } = this.props;

    dispatch({
      type: 'createCrowdTag/updateState',
      payload: {
        isSelectedGrantedTable: false,
        isAddGrant: !isAddGrant,
      },
    });

    setFieldsValue({
      data: undefined,
      field: undefined,
    });
  };

  onTableChange = val => {
    const {
      dispatch,
      form: { setFieldsValue },
    } = this.props;

    setFieldsValue({
      primaryKey: undefined,
    });
    // 查询所选表中的所有字段
    dispatch({
      type: 'createCrowdTag/queryTableColumns',
      payload: {
        tableGuid: val,
      },
    });
  };

  onGrantedTableChange = val => {
    const {
      dispatch,
      form: { setFieldsValue },
    } = this.props;
    const Promises = [
      dispatch({
        type: 'createCrowdTag/updateState',
        payload: {
          isAddGrant: false,
          isSelectedGrantedTable: true,
        },
      }),
      dispatch({
        type: 'createCrowdTag/queryGrantedTableColumns',
        payload: {
          tableGuid: val,
        },
      }),
      dispatch({
        type: 'createCrowdTag/queryOdpsGrantedTableByTableName',
        payload: {
          tableName: val,
        },
      }),
    ];

    Promise.all(Promises);

    // 查询所选表中的所有字段

    setFieldsValue({
      field: undefined,
    });
  };

  onSubmitAddGrantedTable = () => {
    const {
      dispatch,
      form: { validateFields },
    } = this.props;

    validateFields(['tableName', 'partitionField', 'primaryKey', 'isGranted'], (err, values) => {
      if (!err) {
        dispatch({
          type: 'createCrowdTag/editGrantedTable',
          payload: { ...values, sceneType: 'LABEL_SCENE' },
        });
      }
    });
  };

  onModalCancel = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createCrowdTag/onModalCancel',
    });
  };

  onModalOpen = () => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createCrowdTag/onModalOpen',
    });
  };

  onMetaTypeChange = e => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createCrowdTag/updateState',
      payload: {
        metaType: e.target.value,
      },
    });
  };

  // 新增标签
  onSubmit = () => {
    const {
      form: { validateFields, getFieldValue },
      dispatch,
      createCrowdTag: { editFormData },
      user: { currentUser },
    } = this.props;
    validateFields((err, values) => {
      if (!err) {
        dispatch({
          type: 'createCrowdTag/editLabel',
          payload: formatSubmit(values, getFieldValue, currentUser, this.longOverTime)
        });
      }
    });
  };

  onLabelEnumValueTableChange = val => {
    const { dispatch } = this.props;

    dispatch({
      type: 'createCrowdTag/queryGrantedTableEnumColumns',
      payload: {
        tableGuid: val,
      },
    });
  };

  onSubmitAddLabelEnumValue = values => {
    const { dispatch, user: { currentUser } } = this.props;

    dispatch({
      type: 'createCrowdTag/editLabelEnumValueObj',
      payload: {
        ...values,
        creator: { empId: currentUser.workId, nickName: currentUser.name }
      },
    });
  };

  handlePropertyNameIsExist = (rule, val, callback) => {
    const {
      dispatch,
      createCrowdTag: { editFormData },
    } = this.props;

    if (!val) {
      callback();
    }

    if (/[\u4E00-\u9FA5]/g.test(val)) {
      callback(new Error('只可输入字母，不能输入汉字数字'))
    }

    dispatch({
      type: 'createCrowdTag/queryLabelByName',
      payload: {
        name: encodeURIComponent(val.trim()),
      },
    }).then(res => {
      if (res && val !== editFormData.code) {
        callback('该标签名已存在');
      }

      callback();
    });
  };

  handleSqlParamsIsExist = (rule, val, callback) => {
    if (val.hasOwnProperty('enumId')) {
      if (val.name && val.description && val.paramType && val.enumId) {
        callback();
      } else {
        callback('请将数据填写完整')
      }
    } else {
      if (val.name && val.description && val.paramType) {
        callback();
      } else {
        callback('请将数据填写完整')
      }
    }

  }

  handleSqlVal = (rule, val, callback) => {
    if (val.match(/\$\{(.*?)\}/g)) {
      const data = Array.from(new Set(val.match(/\$\{(.*?)\}/g).filter(item => !item.toLocaleLowerCase() !== '${date}').map(ele => ele.slice(2, -1))))
      if (data && data.length > 0 && !data.includes("")) {
        callback();
      } else {
        data = []
        callback()
      }
    } else {
      callback()
    }
  }


  editorChange = (value) => {
    const {
      dispatch,
      form: { setFieldsValue },
    } = this.props;

    setFieldsValue({
      sqlTemplate: value,
    });
    dispatch({
      type: 'createCrowdTag/editorVariableList',
      payload: value,
    })
  }

  radioChange = (e) => {
    if (e.target.value === 1) {
      const {
        dispatch
      } = this.props;

      dispatch({
        type: 'createCrowdTag/editorVariableList',
        payload: 'select distinct xxx as user_id from ...',
      })
    }
  }

  onTypeChange = (e) => {
    const { form: { setFieldsValue } } = this.props;
    if (e.target.value === 'REALTIME') {
      setFieldsValue({ portraitType: 'tbup' })
    }
  }

  // 画像类型操作
  onPortraitTypeChange = (e) => {
    const { form: { setFieldsValue } } = this.props;
    if (e.target.value === 'device_p') {
      setFieldsValue({ source: 'API' })
    }
  }

  render() {
    const {
      createCrowdTag: {
        onCancel = NOOP,
        onSubmit = NOOP,
        editFormData,
        grantedTables = [],
        grantedTablesEnumValue = [], // 枚举值所用的授权表
        odpsTables = [],
        tableColumns = [],
        isAddGrant = false,
        labelColumns = [],
        labelEnumColumns = [],
        isSelectedGrantedTable = false,
        keyword,
        modalVisible,
        treeData,
        metaType,
        bizEntities,
        entityName,
        labelEnumMapping,
        labelDataTypes,
        loading,
        enumValueEditFormData,
        usageScen,
        btnLoading,
      },
      form: { getFieldDecorator, getFieldValue, setFieldsValue },
    } = this.props;
    const serviceParams = get(editFormData, 'sourceConfig.apiSourceExtInfo.serviceParams', [])
    return (
      <PageHeaderWrapper title="创建标签">
        <Card bordered={false} style={{ minHeight: 1282 }} loading={loading}>
          <Form {...FORM_ITEM_LAYOUT} onSubmit={onSubmit}>
            <FormItem label="英文名称">
              {getFieldDecorator('code', {
                initialValue: editFormData.code,
                rules: [
                  { required: true, message: '请输入标签英文名称' },
                  {
                    validator: this.handlePropertyNameIsExist,
                  },
                ],
              })(
                <Input placeholder="请输入标签英文名称" onChange={this.handlePropertyNameChange} />
              )}
            </FormItem>
            <FormItem label="中文名称">
              {getFieldDecorator('name', {
                initialValue: editFormData.name,
                rules: [{ required: true, message: '请输入标签中文名称' }],
              })(<Input placeholder="请输入标签中文名称" />)}
            </FormItem>
            <FormItem label="标签描述">
              {getFieldDecorator('description', {
                initialValue: editFormData.description,
                rules: [{ required: true, message: '请输入标签描述' }],
              })(<TextArea placeholder="请输入标签描述" />)}
            </FormItem>
            {/* <FormItem label="关联实体">
              {getFieldDecorator('bizEntityName', {
                initialValue: entityName !== null ? entityName : undefined,
                rules: [{ required: true, message: '请关联实体' }],
              })(
                <Select placeholder="请选择关联实体" disabled>
                  {bizEntities.length > 0 &&
                    bizEntities.map(b => <Option key={b.name}>{b.desc}</Option>)}
                </Select>
              )}
            </FormItem> */}
            <FormItem
              label="标签时效"
              extra={<span style={{ color: 'red' }}>选择实时/离线保存之后无法更改</span>}
            >
              {getFieldDecorator('type', {
                initialValue: editFormData.type,
                rules: [{ required: true, message: '请选择实时或离线' }],
              })(
                <RadioGroup disabled={!!editFormData.id} onChange={this.onTypeChange}>
                  {/* <Radio key="1" value={1}>
                    实时
                  </Radio>
                  <Radio key="3" value={2}>
                    实时-RTUS
                  </Radio>
                  <Radio key="2" value={0}>
                    离线
                  </Radio> */}
                  <Radio key="1" value='REALTIME'>
                    实时
                  </Radio>
                  <Radio key="2" value='OFFLINE'>
                    离线
                  </Radio>
                </RadioGroup>
              )}
            </FormItem>
            {/* https://yuque.antfin-inc.com/zhuge/workbench/gbdoyg#mOZpW */}
            <FormItem label="用户主键">
              {getFieldDecorator('portraitType', {
                initialValue: editFormData.portraitType,
                rules: [{ required: true, message: '请选择账号类型' }],
              })(<RadioGroup disabled={!!editFormData.id} onChange={this.onPortraitTypeChange}>
                <Radio key="1" value='tbup'>
                  淘宝id
                </Radio>
                {
                  getFieldValue('type') !== 'REALTIME' && <Radio key="2" value='scrm_up'> scrm用户画像</Radio>
                }
                <Radio key="3" value='device_p'>设备id</Radio>
              </RadioGroup>)}
            </FormItem>
            {
              getFieldValue('type') === 'OFFLINE' ? <FormItem
                label="更新周期"
              >
                {getFieldDecorator('updatePeriod', {
                  initialValue: editFormData.updatePeriod,
                  rules: [{ required: true, message: '请选择更新周期' }],
                })(
                  <RadioGroup disabled={!!editFormData.id}>
                    <Radio key="1" value="DAY">
                      日更新
                    </Radio>
                    <Radio key="2" value="WEEK">
                      周更新
                    </Radio>
                  </RadioGroup>
                )}
              </FormItem> : null
            }
            {
              getFieldValue('type') === 'REALTIME' ? <FormItem
                label="实时类型"
              >
                {getFieldDecorator('source', {
                  initialValue: editFormData.source,
                  rules: [{ required: true, message: '请选择实时类型' }],
                })(
                  <RadioGroup>
                    {
                      getFieldValue('portraitType') !== 'device_p' &&
                      <Fragment>
                        <Radio key="LINDORM" value="LINDORM">
                          MetaQ
                        </Radio>
                        <Radio key="HSF" value="HSF">
                          IGraph
                        </Radio>
                      </Fragment>
                    }
                    <Radio key="API" value="API">
                      HSF
                    </Radio>
                  </RadioGroup>
                )}
              </FormItem> : null
            }
            {
              getFieldValue('type') === 'OFFLINE' ? <FormItem
                label="离线类型"
              >
                {getFieldDecorator('source', {
                  initialValue: editFormData.source,
                  rules: [{ required: true, message: '请选择离线类型' }],
                })(
                  <RadioGroup>
                    <Radio key="ODPS" value="ODPS">
                      ODPS
                    </Radio>
                  </RadioGroup>
                )}
              </FormItem> : null
            }

            <FormItem label="所属租户">
              {getFieldDecorator('bizRegionList', {
                initialValue: editFormData.bizRegionList,
                rules: [{ required: true, message: '请选择租户' }],
              })(
                <CheckboxGroup>
                  <Checkbox value="merchant_region">商家租户</Checkbox>
                  <Checkbox value="public_region">小二租户</Checkbox>
                  <Checkbox value="fliggy_commercialization">商业化租户</Checkbox>
                </CheckboxGroup>
              )}
            </FormItem>

            <FormItem label="安全等级">
              {getFieldDecorator('securityLevel', {
                initialValue: editFormData.securityLevel && editFormData.securityLevel.toString(),
                rules: [{ required: true, message: '请选择安全等级' }],
              })(
                <Select placeholder="请选择安全等级" style={{ width: 240 }} >
                  <Option value="1">L1</Option>
                  <Option value="2">L2</Option>
                  <Option value="3">L3</Option>
                  <Option value="4">L4</Option>
                </Select>
              )}
              <Popover content={SAFE_TIP}>
                <Button type="link">教我选择</Button>
              </Popover>
            </FormItem>

            <FormItem label="匹配审核">
              {getFieldDecorator('needApproval', {
                initialValue: get(editFormData, 'extInfo.needApproval', undefined) || false,
              })(
                <RadioGroup>
                  <Radio key="1" value={true}>
                    是
                  </Radio>
                  <Radio key="2" value={false}>
                    否
                  </Radio>
                </RadioGroup>
              )}
            </FormItem>

            <FormItem
              label="收费标签"
            >
              {getFieldDecorator('isPay', {
                initialValue: editFormData.isPay,
                rules: [{ required: true, message: '请选择收费标签' }],
              })(
                <RadioGroup>
                  <Radio key="1" value={0}>
                    免费
                  </Radio>
                  <Radio key="2" value={1}>
                    收费
                  </Radio>
                </RadioGroup>
              )}
            </FormItem>

            <FormItem label="业务负责人">
              {getFieldDecorator('bizOwner', {
                initialValue: editFormData.bizOwner,
                rules: [{ required: true, message: '请选择业务负责人' }],
              })(<GetUser mode="default" />)}
            </FormItem>
            <FormItem label="质量负责人">
              {getFieldDecorator('qualityOwner', {
                initialValue: editFormData.qualityOwner,
                rules: [{ required: true, message: '请选择质量负责人' }],
              })(<GetUser mode="default" />)}
            </FormItem>
            <FormItem label="数据负责人">
              {getFieldDecorator('dataOwner', {
                initialValue: editFormData.dataOwner,
                rules: [{ required: true, message: '请选择数据负责人' }],
              })(<GetUser mode="default" />)}
            </FormItem>
            <FormItem label="统计方式">
              {getFieldDecorator('produceType', {
                initialValue: editFormData.produceType,
                rules: [{ required: true, message: '请选择数据负责人' }],
              })(
                <Select placeholder="请选择统计方式">
                  <Option key="ETL">ETL</Option>
                  <Option key="MODEL">MODEL</Option>
                </Select>
              )}
            </FormItem>
            {entityName === 'TAOBAO_USER' && (
              <FormItem label="标签分类">
                {getFieldDecorator('categoryIdNew', {
                  initialValue: this.getCategoryInitialValue(editFormData.categoryIdNew),
                  rules: [{ required: true, message: '请选择标签分类' }],
                })(<MyCascader treeData={treeData} />)}
              </FormItem>
            )}

            <FormItem label="标签来源">
              {getFieldDecorator('dataSource', {
                initialValue: get(editFormData, 'extInfo.dataSourceConfig.dataSource', undefined),
              })(
                <Select placeholder="请选择标签来源">
                  {
                    Object.keys(LABEL_DATA_SOURCE).map(ele => (
                      <Option key={ele}>{LABEL_DATA_SOURCE[ele]}</Option>
                    ))
                  }
                </Select>
              )}
            </FormItem>

            {
              getFieldValue('dataSource') === 'ADEX' && [
                <FormItem label="原标签ID">
                  {getFieldDecorator('adexLabelId', {
                    initialValue: get(editFormData, 'extInfo.dataSourceConfig.adexLabelId', undefined),
                  })(<Input placeholder="请输入原标签ID" />)}
                </FormItem>,
                <FormItem label="原数据表">
                  {getFieldDecorator('adexTableName', {
                    initialValue: get(editFormData, 'extInfo.dataSourceConfig.adexTableName', undefined),
                  })(<Input placeholder="请输入原数据表" />)}
                </FormItem>
              ]
            }


            {getFieldValue('type') === 'REALTIME' && getFieldValue('source') !== 'API' && (
              <FormItem label="标签过期时间">
                <FormItem name="overtimeType">
                  {getFieldDecorator('overtimeType', {
                    initialValue: OverTimeType(editFormData.expiredType)
                    // initialValue: editFormData.ttl === 24? 'today': (editFormData.ttl >= (this.longOverTime / 3600)) ? 'long' : 'short',
                  })(
                    <Select placeholder="是否过期" style={{ width: 240 }} >
                      <Option value="long">不过期</Option>
                      <Option value="short">手动在下方设置</Option>
                      <Option value="today">当日过期（0-24点）</Option>
                    </Select>
                  )}
                </FormItem>
                {
                  getFieldValue('overtimeType') === 'short' && (
                    <FormItem>
                      {getFieldDecorator('ttl', {
                        initialValue: editFormData.ttl,
                        rules: [{ required: true, message: '过期时间' }],
                      })(<MyInputNumber style={{ width: 130 }} suffix="H（小时）" />)}
                    </FormItem>
                  )
                }
              </FormItem>
            )}

            {getFieldValue('type') === 'REALTIME' && getFieldValue('source') === 'API' && (
              <FormItem label="标签过期时间">
                <FormItem name="overtimeType">
                  {getFieldDecorator('overtimeType', {
                    initialValue: editFormData.expiredType ? OverTimeType(editFormData.expiredType) : 'long'
                  })(
                    <Select placeholder="是否过期" style={{ width: 240 }} >
                      <Option value="long">不过期</Option>
                    </Select>
                  )}
                </FormItem>
              </FormItem>
            )}

            {
              (getFieldValue('source') === 'LINDORM' && getFieldValue('source') !== 'API' && getFieldValue('type') === "REALTIME") && (
                <FormItem label="数据表">
                  {getFieldDecorator('sourceGuid', {
                    initialValue: 'tripODS.trip_picasso_user_profile',
                    rules: [{ required: true, message: '请输入数据表' }],
                  })(<Select />)}
                </FormItem>
              )
            }
            {getFieldValue('type') === "OFFLINE" && (
              <>
                <FormItem label="数据表">
                  {getFieldDecorator('sourceGuid', {
                    initialValue: editFormData.sourceGuid,
                    rules: [{ required: true, message: '请选择数据表' }],
                  })(
                    <Select
                      showSearch
                      placeholder="请选择数据表"
                      onChange={this.onGrantedTableChange}
                    >
                      {grantedTables.length && grantedTables.map(t => <Option key={t}>{t}</Option>)}
                    </Select>
                  )}
                </FormItem>
                <FormItem label=" " colon={false}>
                  <Button disabled={isSelectedGrantedTable} onClick={this.toggle}>{`${isAddGrant ? '取消' : ''
                    }新增授权`}</Button>
                </FormItem>
              </>
            )}
            {
              (getFieldValue('source') !== 'LINDORM' && getFieldValue('source') !== 'API' && getFieldValue('type') === "REALTIME") && (
                <FormItem label="数据表">
                  {getFieldDecorator('sourceGuid', {
                    initialValue: editFormData.sourceGuid,
                    rules: [{ required: true, message: '请输入数据表' }],
                  })(<Input />)}
                </FormItem>
              )
            }

            {((getFieldValue('type') === "REALTIME" && getFieldValue('source') === 'API')) && [
              <FormItem label="完整路径服务名称" key="serviceName">
                {getFieldDecorator('serviceName', {
                  initialValue: get(editFormData, 'sourceConfig.apiSourceExtInfo.serviceName', null),
                  rules: [{ required: true, message: '请输入完整路径服务名称' }],
                })(<Input placeholder="请输入完整路径服务名称" />)}
              </FormItem>,
              <FormItem label="方法名" key="methodName">
                {getFieldDecorator('methodName', {
                  initialValue: get(editFormData, 'sourceConfig.apiSourceExtInfo.methodName', null),
                  rules: [{ required: true, message: '请输入方法名' }],
                })(<Input placeholder="请输入方法名" />)}
              </FormItem>,
              <FormItem label="版本号" key="proServiceVersion">
                {getFieldDecorator('proServiceVersion', {
                  initialValue: get(editFormData, 'sourceConfig.apiSourceExtInfo.proServiceVersion', null),
                  rules: [{ required: true, message: '请输入版本号' }],
                })(<Input placeholder="请输入版本号" />)}
              </FormItem>,
              <FormItem label="超时时间" key="timeout">
                {getFieldDecorator('timeout', {
                  initialValue: get(editFormData, 'sourceConfig.apiSourceExtInfo.timeout', null),
                  rules: [{ required: true, message: '请输入版本号' }],
                })(<Input placeholder="请输入超时时间，单位ms" />)}
              </FormItem>,
              <FormItem label="结果提取" key="resultKey">
                {getFieldDecorator('resultKey', {
                  initialValue: get(editFormData, 'sourceConfig.apiSourceExtInfo.resultKey', null),
                  rules: [{ required: true, message: '请输入结果提取，jsonPath格式' }],
                })(<TextArea placeholder="请输入结果提取" style={{ width: '80%' }} />)}
                <Button type="link" onClick={() => window.open('https://github.com/alibaba/fastjson/wiki/JSONPath')}>jsonPath文档</Button>
              </FormItem>,
              isArrayEmptyOrHasObject(serviceParams) === 'string' ?
              <ServiceParams form={this.props.form} value={serviceParams} />
              : <FormItem label="入参" key="serviceParams" wrapperCol={{ span: 18 }}>
                {getFieldDecorator('serviceParams', {
                  initialValue: serviceParams,
                })(<EditableTreeTable />)}
              </FormItem>,
              editFormData.hsfParams && editFormData.hsfParams.length > 0 && (
                <FormItem label="标签参数" required={true}>
                  {editFormData.hsfParams.map((e, index) => {
                    return <FormItem key={index} {...FORM_ITEM_LAB} className={styles.crowdErr}>
                      {getFieldDecorator(`hsfParams[${index}]`, {
                        initialValue: e,
                        rules: [
                          { required: true, message: '请将数据填写完成' },
                          {
                            validator: this.handleSqlParamsIsExist,
                          },
                        ],
                      })(
                        <SqlParams
                          type= 'hsf' // 用于区别sql语句还是hsf
                          labelEnumMapping={labelEnumMapping}
                          onModalOpen={this.onModalOpen}
                          sqlData={e}
                          style={{ flex: 1 }}
                        />
                      )}
                    </FormItem>
                  }
                  )}
                </FormItem>
              )
            ]}

            {/* 离线才需要授权 */}
            {(isAddGrant || (isSelectedGrantedTable && getFieldValue('type') === "OFFLINE")) && (
              <Fragment>
                {!isSelectedGrantedTable && (
                  <FormItem label="表">
                    {getFieldDecorator('tableName', {
                      initialValue: editFormData.tableName,
                      rules: [{ required: true, message: '请输入表' }],
                    })(
                      <Select
                        showSearch
                        onSearch={this.onTableSearch}
                        onChange={this.onTableChange}
                        placeholder="选择表"
                        optionLabelProp="value"
                      >
                        {odpsTables.length && // 高亮显示
                          odpsTables.map(t => {
                            const index = t.indexOf(keyword);
                            const beforeStr = t.substr(0, index);
                            const afterStr = t.substr(index + keyword.length);
                            const content =
                              index > -1 ? (
                                <span>
                                  {beforeStr}
                                  <span style={{ color: '#f50' }}>{keyword}</span>
                                  {afterStr}
                                </span>
                              ) : (
                                <span>{t}</span>
                              );
                            return <Option key={t}>{content}</Option>;
                          })}
                      </Select>
                    )}
                  </FormItem>
                )}

                <FormItem label="分区分段名">
                  {getFieldDecorator('partitionField', {
                    initialValue: editFormData.partitionField,
                    rules: [{ required: false, message: '请输入分区分段名' }],
                  })(<Input disabled={isSelectedGrantedTable} placeholder="请输入分区分段名" />)}
                </FormItem>
                <FormItem label="用户主键字段">
                  {getFieldDecorator('primaryKey', {
                    initialValue: editFormData.primaryKey,
                    rules: [{ required: false, message: '请输入字段' }], // 服务端数据问题先放开做测试
                  })(
                    <Select showSearch placeholder="请输入字段" disabled={isSelectedGrantedTable}>
                      {tableColumns.length &&
                        tableColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
                    </Select>
                  )}
                </FormItem>
                <FormItem
                  label="确认授权"
                  extra={
                    !isSelectedGrantedTable ? (
                      <span style={{ color: 'red' }}>
                        add user aliyun$<EMAIL>;
                        GRANT Describe,Select ON TABLE 表名称 TO USER ALIYUN$<EMAIL>;
                        GRANT LABEL 2 ON TABLE 表名称 TO USER ALIYUN$<EMAIL>;
                      </span>
                    ) : null
                  }
                >
                  {getFieldDecorator('isGranted', {
                    initialValue: editFormData.isGranted,
                    rules: [{ required: true, message: '请确认授权' }],
                  })(
                    // <CheckboxGroup disabled={isSelectedGrantedTable}>
                    <CheckboxGroup>
                      <Checkbox value="hasGranted">已经授权</Checkbox>
                    </CheckboxGroup>
                  )}
                </FormItem>
                {!isSelectedGrantedTable && (
                  <FormItem label=" " colon={false}>
                    <Button onClick={this.onSubmitAddGrantedTable}>添加表</Button>
                  </FormItem>
                )}
              </Fragment>
            )}

            {getFieldValue('type') === "OFFLINE" && (
              <FormItem label="标签字段">
                {getFieldDecorator('field', {
                  initialValue: editFormData.field,
                  rules: [{ required: true, message: '请选择字段' }],
                })(
                  <Select showSearch placeholder="请选择字段">
                    {labelColumns.length &&
                      labelColumns.map(c => <Option key={c.columnName}>{c.columnName}</Option>)}
                  </Select>
                )}
              </FormItem>
            )}

            {getFieldValue('type') === "OFFLINE" && (
              <FormItem label=" 标签定义方式">
                {getFieldDecorator('definition', {
                  initialValue: editFormData.definition,
                  rules: [{ required: true, message: '请选择标签定义方式' }],
                })(
                  <RadioGroup onChange={(e) => this.radioChange(e)} disabled={!!editFormData.id}>
                    <Radio key="1" value={6}>
                      快速定义
                    </Radio>
                    <Radio key="2" value={1}>
                      使用SQL定义
                    </Radio>
                  </RadioGroup>
                )}
              </FormItem>
            )}
            {((getFieldValue('source') === 'LINDORM' && getFieldValue('type') === "REALTIME") || (getFieldValue('source') === 'HSF' && getFieldValue('type') === "REALTIME") || (getFieldValue('source') === 'API' && getFieldValue('type') === "REALTIME")) && (
              <FormItem label="标签支持项">
                {getFieldDecorator('scope', {
                  initialValue: editFormData.scope || ['MATCH'],
                  rules: [{ required: true, message: '请选择使用场景' }],
                })(
                  <CheckboxGroup>
                    {/* <Checkbox value="PUSH">圈人</Checkbox> */}
                    <Checkbox value="MATCH">在线匹配</Checkbox>
                    {/* <Checkbox value="ANALYSIS">画像分析</Checkbox> */}
                  </CheckboxGroup>
                )}
              </FormItem>
            )}


            {((getFieldValue('definition') === 6 && getFieldValue('type') === "OFFLINE") || (getFieldValue('source') === 'HOLO' && getFieldValue('type') === "REALTIME")) && (
              <FormItem label="标签支持项">
                {getFieldDecorator('scope', {
                  initialValue: editFormData.scope || ['PUSH'],
                  rules: [{ required: true, message: '请选择使用场景' }],
                })(
                  <CheckboxGroup>
                    <Checkbox value="PUSH">圈人</Checkbox>
                    <Checkbox value="MATCH">在线匹配</Checkbox>
                    <Checkbox value="ANALYSIS">画像分析</Checkbox>
                  </CheckboxGroup>
                )}
              </FormItem>
            )}

            {((getFieldValue('definition') === 1 && getFieldValue('type') === "OFFLINE")) && (
              <FormItem label="标签支持项">
                {getFieldDecorator('scope', {
                  initialValue: editFormData.scope || ['PUSH'],
                  rules: [{ required: true, message: '请选择使用场景' }],
                })(
                  <CheckboxGroup>
                    <Checkbox value="PUSH">圈人</Checkbox>
                    {/* <Checkbox value="MATCH">在线匹配</Checkbox> */}
                    {/* <Checkbox value="ANALYSIS">画像分析</Checkbox> */}
                  </CheckboxGroup>
                )}
              </FormItem>
            )}

            {getFieldValue('definition') === 1 && getFieldValue('type') === "OFFLINE" && (
              <FormItem label="SQL">
                {getFieldDecorator('sqlTemplate', {
                  initialValue: editFormData.sqlTemplate,
                  rules: [
                    { required: true, message: '请输入符合要求的sql语句' },
                    {
                      validator: this.handleSqlVal,
                    },
                  ],
                })(
                  // <Editor
                  //   onChange={this.editorChange}       
                  //   width="100%"
                  //   height="200px"
                  //   language="sql"
                  //   theme="vs-dark"
                  // />
                  <CodeMirror mode="sql" editorChange={this.editorChange} style={{ lineHeight: '20px', height: '300px' }} />
                )}
              </FormItem>
            )}



            {getFieldValue('definition') === 1 && getFieldValue('type') === "OFFLINE" && editFormData.sqlParams && editFormData.sqlParams.length > 0 && (
              <FormItem label="SQL参数" required={true}>
                {editFormData.sqlParams && editFormData.sqlParams.length > 0 && editFormData.sqlParams.map((e, index) => {
                  return <FormItem key={index} {...FORM_ITEM_LAB} className={styles.crowdErr}>
                    {getFieldDecorator(`sqlParams[${index}]`, {
                      initialValue: e,
                      rules: [
                        { required: true, message: '请将数据填写完成' },
                        {
                          validator: this.handleSqlParamsIsExist,
                        },
                      ],
                    })(
                      <SqlParams
                        labelEnumMapping={labelEnumMapping}
                        onModalOpen={this.onModalOpen}
                        sqlData={e}
                        style={{ flex: 1 }}
                      />
                    )}
                  </FormItem>
                }
                )}
              </FormItem>
            )}

            {(getFieldValue('definition') === 6 || getFieldValue('type') === 'REALTIME') && (
              <FormItem label="标签值类型">
                {getFieldDecorator('dataType', {
                  initialValue: editFormData.dataType,
                  rules: [{ required: true, message: '请选择标签值类型' }],
                })(
                  <RadioGroup disabled={(!!editFormData.id) && (getFieldValue('type') === 'OFFLINE')}>
                    {labelDataTypes.length > 0 &&
                      labelDataTypes.map(l => {
                        const [value, label] = Object.entries(l)[0];
                        return (
                          <Radio key={value} value={value}>
                            {label}
                          </Radio>
                        );
                      })}
                  </RadioGroup>
                )}
              </FormItem>
            )}

            {((((getFieldValue('dataType') === 'ENUM') ||
              (getFieldValue('dataType') === 'KV') ||
              (getFieldValue('dataType') === 'MULTI_VALUE')) && (getFieldValue('type') === 'REALTIME')) || ((getFieldValue('type') === 'OFFLINE') && (getFieldValue('definition') === 6) && ((getFieldValue('dataType') === 'ENUM') ||
                (getFieldValue('dataType') === 'KV') ||
                (getFieldValue('dataType') === 'MULTI_VALUE')))) && (
                <FormItem label="枚举映射" extra={<Button onClick={this.onModalOpen}>新增</Button>}>
                  {getFieldDecorator('dimEnumMetaId', {
                    initialValue: editFormData.dimEnumMetaId,
                    rules: [
                      {
                        required: true,
                        message: '请选择枚举映射类型',
                      },
                    ],
                  })(
                    <Select
                      showSearch
                      placeholder="请选择枚举映射"
                      filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    >
                      {labelEnumMapping.length &&
                        labelEnumMapping.map(c => (
                          <Option key={c.id} value={c.id}>
                            {`${c.name}（${c.code})`}
                          </Option>
                        ))}
                    </Select>
                  )}
                </FormItem>
              )}
            {((getFieldValue('dataType') === 'KV' && getFieldValue('type') === 'REALTIME') || (getFieldValue('dataType') === 'KV' && getFieldValue('type') === 'OFFLINE' && getFieldValue('definition') === 6)) && [
              <FormItem label="最小值" key="minValue">
                {getFieldDecorator('minValue', {
                  initialValue: editFormData.minValue,
                  rules: [{ required: true, message: '请输入最小值' }],
                })(<Input placeholder="请输入最小值" />)}
              </FormItem>,
              <FormItem label="最大值" key="maxValue">
                {getFieldDecorator('maxValue', {
                  initialValue: editFormData.maxValue,
                  rules: [{ required: true, message: '请输入最大值' }],
                })(<Input placeholder="请输入最大值" />)}
              </FormItem>,
              <FormItem label="步长" key="stepLength">
                {getFieldDecorator('stepLength', {
                  initialValue: editFormData.stepLength,
                  rules: [{ required: true, message: '请输入步长' }],
                })(<Input placeholder="请输入步长" />)}
              </FormItem>,
            ]}

            <Row gutter={24}>
              <Col span={4} />
              <Col span={14} style={{ textAlign: 'center' }}>
                <Link to={`/develop-tool/label-management`}>
                  <Button onClick={() => onCancel()} style={{ marginRight: 15 }}>
                    取消
                  </Button>
                </Link>
                <Button type="primary" onClick={this.onSubmit} loading={btnLoading}>
                  保存
                </Button>
              </Col>
            </Row>
          </Form>
        </Card>

        <CreateTagEnum
          metaType={metaType}
          onMetaTypeChange={this.onMetaTypeChange}
          grantedTables={grantedTablesEnumValue}
          modalVisible={modalVisible}
          onCancel={this.onModalCancel}
          labelEnumColumns={labelEnumColumns}
          onLabelEnumValueTableChange={this.onLabelEnumValueTableChange}
          onSubmit={this.onSubmitAddLabelEnumValue}
          editFormData={enumValueEditFormData}
        />
      </PageHeaderWrapper>

    );
  }
}

export default CreateCrowdTag;
